#!/usr/bin/env python3
"""
R<PERSON>lý test pro reprodukci zaseknutí execute_command()
"""

import asyncio
import sys
import os
import time

# Přidáme cestu k managers
sys.path.append(os.path.join(os.path.dirname(__file__), 'managers'))

from electronics_api import execute_command

async def spam_execute_command(count=10):
    """
    Spamuje execute_command() - nejpravděpodobněj<PERSON><PERSON> způsob jak způsobit zaseknutí
    """
    print(f"🚀 Spouštím {count} execute_command() současně...")
    print("   <PERSON><PERSON><PERSON><PERSON><PERSON>, zda se některé zaseknou po 'ano 1'")
    print("-" * 50)
    
    tasks = []
    for i in range(count):
        # Použijeme skutečný příkaz k boxctl-carel.py
        cmd = ["python", "boxctl-carel.py", "-d", "COM5", "-a", "heart_beat"]
        task = asyncio.create_task(execute_command(cmd, f"test_{i}"))
        tasks.append(task)
        print(f"[{time.strftime('%H:%M:%S.%f')[:-3]}] Spuštěn task {i}")
        
        # Velmi krátká pauza
        await asyncio.sleep(0.01)
    
    print(f"\n⏰ Čekám na dokončení (timeout 15s)...")
    
    try:
        results = await asyncio.wait_for(asyncio.gather(*tasks), timeout=15)
        print(f"✅ Všechny tasky dokončeny: {len(results)}")
        
        # Spočítáme úspěšné
        successful = sum(1 for r in results if r[2] is None)
        print(f"   Úspěšné: {successful}/{len(results)}")
        
    except asyncio.TimeoutError:
        print("⚠️  TIMEOUT! Některé tasky se ZASEKLY!")
        
        completed = 0
        hanging = 0
        
        for i, task in enumerate(tasks):
            if task.done():
                completed += 1
                try:
                    result = task.result()
                    status = "✅" if result[2] is None else "❌"
                    print(f"   Task {i}: {status} dokončen")
                except Exception as e:
                    print(f"   Task {i}: ❌ chyba - {e}")
            else:
                hanging += 1
                print(f"   Task {i}: 🔄 VISÍ - zasekl se po 'ano 1'!")
                task.cancel()
        
        print(f"\n📊 VÝSLEDKY:")
        print(f"   Dokončené: {completed}")
        print(f"   Zaseknuté: {hanging}")
        
        if hanging > 0:
            print(f"\n🎯 PROBLÉM REPRODUKOVÁN!")
            print(f"   {hanging} tasků se zaseklo po 'ano 1'")
            print(f"   Příčina: ThreadPoolExecutor deadlock")

async def single_test():
    """Jednoduchý test jednoho volání"""
    print("🔍 Test jednoho volání execute_command()...")
    
    cmd = ["python", "boxctl-carel.py", "-d", "COM5", "-a", "heart_beat"]
    
    try:
        result = await asyncio.wait_for(execute_command(cmd, "single"), timeout=10)
        print(f"✅ Úspěch: {result[0][:50]}...")
    except asyncio.TimeoutError:
        print("⏰ TIMEOUT! I jeden task se zasekl!")
    except Exception as e:
        print(f"❌ Chyba: {e}")

def main():
    if len(sys.argv) < 2:
        print("Použití:")
        print("  python quick_hang_test.py single    # test jednoho volání")
        print("  python quick_hang_test.py spam [N]  # spam test (default N=10)")
        print("")
        print("Příklady:")
        print("  python quick_hang_test.py single")
        print("  python quick_hang_test.py spam 5")
        print("  python quick_hang_test.py spam 20")
        sys.exit(1)
    
    test_type = sys.argv[1]
    
    if test_type == "single":
        print("🧪 SINGLE TEST - execute_command() zaseknutí")
        print("=" * 50)
        asyncio.run(single_test())
        
    elif test_type == "spam":
        count = int(sys.argv[2]) if len(sys.argv) > 2 else 10
        print("🧪 SPAM TEST - execute_command() zaseknutí")
        print("=" * 50)
        asyncio.run(spam_execute_command(count))
        
    else:
        print(f"❌ Neznámý test: {test_type}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚡ Test přerušen")
    except Exception as e:
        print(f"\n❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
