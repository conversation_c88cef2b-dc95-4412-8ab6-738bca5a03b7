#!/usr/bin/env python3
"""
Rychlý test pro simulaci problému s hromaděním př<PERSON>azů
"""

import subprocess
import time
import sys
import os

def quick_spam_test(device: str, box_count: int = 5):
    """
    Rych<PERSON> pošle několik unlock příkazů za sebou
    """
    print(f"🚀 RYCHLÝ SPAM TEST - {box_count} příkazů za sebou")
    print(f"Zařízení: {device}")
    print(f"Čas: {time.strftime('%H:%M:%S')}")
    print("-" * 40)
    
    # Pošleme příkazy velmi rychle za sebou
    for i in range(1, box_count + 1):
        cmd = [
            "python", "boxctl-carel.py",
            "-d", device,
            "-a", "unlock", 
            "-v", f"{i:02d}"
        ]
        
        print(f"[{time.strftime('%H:%M:%S.%f')[:-3]}] Posílám unlock na schránku {i}")
        
        # Spustíme příkaz na pozadí (neblokující)
        try:
            subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
        except Exception as e:
            print(f"❌ Chyba: {e}")
        
        # Velmi krátká pauza (simuluje rychlé klikání)
        time.sleep(0.05)
    
    print(f"\n✅ Odesláno {box_count} příkazů během {box_count * 0.05:.2f} sekund")
    print("⚠️  Nyní sledujte, zda se schránky otevřou okamžitě nebo s velkým zpožděním...")

def interrupt_test(device: str):
    """
    Test přerušování příkazů
    """
    print(f"\n🔥 TEST PŘERUŠOVÁNÍ")
    print("-" * 40)
    
    for i in range(1, 4):
        cmd = [
            "python", "boxctl-carel.py",
            "-d", device,
            "-a", "unlock",
            "-v", f"{i:02d}"
        ]
        
        print(f"[{time.strftime('%H:%M:%S')}] Spouštím příkaz {i} a přeruším ho...")
        
        try:
            # Spustíme proces
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            
            # Počkáme chvilku a zabijeme proces
            time.sleep(1.5)
            process.terminate()
            print(f"[{time.strftime('%H:%M:%S')}] ⚡ Proces {i} přerušen")
            
        except Exception as e:
            print(f"❌ Chyba: {e}")
        
        time.sleep(0.2)
    
    print("✅ Test přerušování dokončen")

def main():
    if len(sys.argv) < 2:
        print("Použití: python quick_test.py <COM_PORT> [počet_schránek]")
        print("Příklad: python quick_test.py COM5 3")
        sys.exit(1)
    
    device = sys.argv[1]
    box_count = int(sys.argv[2]) if len(sys.argv) > 2 else 5
    
    print("🧪 RYCHLÝ TEST PROBLÉMU SE SÉRIOVÝM PORTEM")
    print("=" * 50)
    
    try:
        # Test 1: Spam příkazy
        quick_spam_test(device, box_count)
        
        # Krátká pauza
        time.sleep(3)
        
        # Test 2: Přerušované příkazy
        interrupt_test(device)
        
        print(f"\n📊 CO SLEDOVAT:")
        print("1. Otevřou se schránky okamžitě nebo až po dlouhé době?")
        print("2. Otevřou se všechny schránky nebo jen některé?")
        print("3. Otevřou se schránky ve správném pořadí?")
        print("4. Zůstanou některé příkazy 'viset' v bufferu?")
        
        print(f"\n⏰ Počkejte několik minut a sledujte, zda se schránky otevřou později...")
        
    except KeyboardInterrupt:
        print(f"\n⚡ Test přerušen")
    except Exception as e:
        print(f"\n❌ Chyba: {e}")

if __name__ == "__main__":
    main()
