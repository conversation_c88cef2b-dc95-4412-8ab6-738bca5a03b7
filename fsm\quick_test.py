#!/usr/bin/env python3
"""
Rychlý test pro simulaci problému s hromaděním příkazů
"""

import subprocess
import time
import sys
import os
from datetime import datetime

def log_time():
    """Vrátí aktuální čas s milisekundami"""
    return datetime.now().strftime('%H:%M:%S.%f')[:-3]

def extreme_spam_test(device: str, box_count: int = 10, rounds: int = 3):
    """
    EXTRÉMNÍ test - pošle mnoho příkazů velmi rychle za sebou ve více kolech
    """
    print(f"� EXTRÉMNÍ SPAM TEST - {box_count} příkazů × {rounds} kol")
    print(f"Zařízení: {device}")
    print(f"Čas: {log_time()}")
    print("-" * 50)

    all_processes = []
    start_time = time.time()

    for round_num in range(1, rounds + 1):
        print(f"\n🔥 KOLO {round_num}/{rounds}")

        # Pošleme příkazy SOUČASNĚ (bez čekání)
        for i in range(1, box_count + 1):
            cmd = [
                "python", "boxctl-carel.py",
                "-d", device,
                "-a", "unlock",
                "-v", f"{i:02d}"
            ]

            print(f"[{log_time()}] → Schránka {i} (kolo {round_num})")

            try:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=os.path.dirname(os.path.abspath(__file__))
                )
                all_processes.append((process, i, round_num, time.time()))
            except Exception as e:
                print(f"❌ Chyba při spouštění: {e}")

            # ŽÁDNÁ pauza - maximální spam!

        # Krátká pauza mezi koly
        time.sleep(0.1)

    total_time = time.time() - start_time
    print(f"\n✅ Odesláno {box_count * rounds} příkazů během {total_time:.3f} sekund")
    print(f"📊 Rychlost: {(box_count * rounds) / total_time:.1f} příkazů/sekunda")

    return all_processes

def chaos_test(device: str):
    """
    CHAOS test - smíšené příkazy, přerušování, různé akce
    """
    print(f"\n🌪️  CHAOS TEST - smíšené příkazy a přerušování")
    print("-" * 50)

    actions = [
        ("unlock", "01"), ("unlock", "02"), ("heart_beat", None),
        ("unlock", "03"), ("get_door_state", "01"), ("unlock", "04"),
        ("unlock", "05"), ("unlock", "01"), ("unlock", "02")
    ]

    processes = []

    for i, (action, value) in enumerate(actions):
        cmd = ["python", "boxctl-carel.py", "-d", device, "-a", action]
        if value:
            cmd.extend(["-v", value])

        print(f"[{log_time()}] 🎯 {action} {value or ''}")

        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            processes.append(process)

            # Každý třetí proces přerušíme
            if i % 3 == 2:
                time.sleep(0.5)
                process.terminate()
                print(f"[{log_time()}] ⚡ Přerušen {action}")

        except Exception as e:
            print(f"❌ Chyba: {e}")

        # Velmi krátké náhodné pauzy
        time.sleep(0.02)

    return processes

def monitor_processes(processes, timeout_minutes=5):
    """
    Monitoruje běžící procesy a hlásí jejich stav
    """
    print(f"\n� MONITORING PROCESŮ (timeout {timeout_minutes} min)")
    print("-" * 50)

    start_time = time.time()
    timeout_seconds = timeout_minutes * 60

    completed = []
    still_running = list(processes)

    while still_running and (time.time() - start_time) < timeout_seconds:
        for process_info in still_running[:]:  # kopie seznamu
            if len(process_info) == 4:  # extreme_spam_test format
                process, box_id, round_num, proc_start_time = process_info
                name = f"Schránka {box_id} (kolo {round_num})"
            else:  # chaos_test format
                process = process_info
                name = f"Proces {process.pid}"

            if process.poll() is not None:  # proces skončil
                duration = time.time() - (proc_start_time if len(process_info) == 4 else start_time)
                _, stderr = process.communicate()

                if process.returncode == 0:
                    print(f"[{log_time()}] ✅ {name} - dokončen ({duration:.2f}s)")
                else:
                    print(f"[{log_time()}] ❌ {name} - chyba {process.returncode} ({duration:.2f}s)")
                    if stderr:
                        print(f"    └─ {stderr.decode().strip()}")

                completed.append(process_info)
                still_running.remove(process_info)

        time.sleep(0.5)  # kontrola každých 0.5s

    # Zabij zbývající procesy
    if still_running:
        print(f"\n⏰ Timeout! Zabíjím {len(still_running)} zbývajících procesů...")
        for process_info in still_running:
            process = process_info[0] if len(process_info) == 4 else process_info
            try:
                process.terminate()
                time.sleep(0.1)
                if process.poll() is None:
                    process.kill()
            except:
                pass

    print(f"\n📊 VÝSLEDKY MONITORINGU:")
    print(f"   Dokončeno: {len(completed)}")
    print(f"   Přerušeno: {len(still_running)}")
    print(f"   Celkový čas: {time.time() - start_time:.2f}s")

    return completed, still_running

def stress_test(device: str, intensity: str = "medium"):
    """
    Stress test s různými úrovněmi intenzity
    """
    intensities = {
        "low": {"box_count": 3, "rounds": 2, "delay": 0.1},
        "medium": {"box_count": 5, "rounds": 3, "delay": 0.05},
        "high": {"box_count": 8, "rounds": 4, "delay": 0.01},
        "extreme": {"box_count": 10, "rounds": 5, "delay": 0.0}
    }

    if intensity not in intensities:
        intensity = "medium"

    config = intensities[intensity]

    print(f"🔥 STRESS TEST - úroveň: {intensity.upper()}")
    print(f"   Schránky: {config['box_count']}")
    print(f"   Kola: {config['rounds']}")
    print(f"   Delay: {config['delay']}s")
    print("-" * 50)

    all_processes = []

    for round_num in range(1, config['rounds'] + 1):
        print(f"\n⚡ KOLO {round_num}/{config['rounds']}")

        for i in range(1, config['box_count'] + 1):
            cmd = [
                "python", "boxctl-carel.py",
                "-d", device,
                "-a", "unlock",
                "-v", f"{i:02d}"
            ]

            print(f"[{log_time()}] 🚀 Schránka {i}")

            try:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=os.path.dirname(os.path.abspath(__file__))
                )
                all_processes.append((process, i, round_num, time.time()))

                if config['delay'] > 0:
                    time.sleep(config['delay'])

            except Exception as e:
                print(f"❌ Chyba: {e}")

    return all_processes

def main():
    if len(sys.argv) < 2:
        print("Použití:")
        print("  python quick_test.py <COM_PORT> [test_type] [intensity]")
        print("")
        print("Test types:")
        print("  spam     - Extrémní spam test (default)")
        print("  chaos    - Chaos test s přerušováním")
        print("  stress   - Stress test s monitoringem")
        print("")
        print("Intensity (pro stress test):")
        print("  low, medium, high, extreme")
        print("")
        print("Příklady:")
        print("  python quick_test.py COM5")
        print("  python quick_test.py COM5 spam")
        print("  python quick_test.py COM5 chaos")
        print("  python quick_test.py COM5 stress extreme")
        sys.exit(1)

    device = sys.argv[1]
    test_type = sys.argv[2] if len(sys.argv) > 2 else "spam"
    intensity = sys.argv[3] if len(sys.argv) > 3 else "medium"

    print("🧪 VYLEPŠENÝ TEST PROBLÉMU SE SÉRIOVÝM PORTEM")
    print("=" * 60)
    print(f"Zařízení: {device}")
    print(f"Test: {test_type}")
    if test_type == "stress":
        print(f"Intenzita: {intensity}")
    print(f"Čas spuštění: {log_time()}")
    print("")

    try:
        processes = []

        if test_type == "spam":
            # Extrémní spam test
            processes = extreme_spam_test(device, box_count=8, rounds=3)

        elif test_type == "chaos":
            # Chaos test
            processes = chaos_test(device)

        elif test_type == "stress":
            # Stress test s monitoringem
            processes = stress_test(device, intensity)

            # Spustíme monitoring
            print(f"\n🔍 Spouštím monitoring procesů...")
            completed, interrupted = monitor_processes(processes, timeout_minutes=3)

            print(f"\n📈 FINÁLNÍ STATISTIKY:")
            print(f"   Úspěšně dokončeno: {len([p for p in completed if p[0].returncode == 0])}")
            print(f"   Chyby: {len([p for p in completed if p[0].returncode != 0])}")
            print(f"   Přerušeno/timeout: {len(interrupted)}")

        else:
            print(f"❌ Neznámý test type: {test_type}")
            sys.exit(1)

        if test_type != "stress":
            print(f"\n📊 CO SLEDOVAT:")
            print("1. 🕐 Otevřou se schránky okamžitě nebo až po dlouhé době?")
            print("2. 📦 Otevřou se všechny schránky nebo jen některé?")
            print("3. 🔢 Otevřou se schránky ve správném pořadí?")
            print("4. 💾 Zůstanou některé příkazy 'viset' v bufferu?")
            print("5. ⚡ Budou některé příkazy timeoutovat?")

            print(f"\n⏰ DŮLEŽITÉ: Počkejte 5-10 minut a sledujte,")
            print("   zda se schránky otevřou později s velkým zpožděním!")
            print(f"\n🎯 Pokud se schránky otevřou až po dlouhé době,")
            print("   problém je POTVRZENÝ - příkazy se hromadí v bufferu!")

    except KeyboardInterrupt:
        print(f"\n⚡ Test přerušen uživatelem")
        print("   (Toto může také přispět k problému s bufferem!)")
    except Exception as e:
        print(f"\n❌ Chyba během testu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
