#!/usr/bin/env python3
"""
Skript pro simulaci problému s hromaděním příkazů v sériovém portu
Tento skript demonstruje, jak se příkazy mohou hromadit a zpracovávat s velkým zpožděním
"""

import subprocess
import time
import threading
import sys
import os
from typing import List
import signal

class SerialBufferSimulator:
    def __init__(self, device: str = "COM5"):
        self.device = device
        self.boxctl_path = "boxctl-carel.py"
        self.running = True
        self.commands_sent = []
        self.responses = []
        
    def send_unlock_command(self, box_id: str, delay: float = 0) -> dict:
        """
        <PERSON><PERSON>le unlock příkaz na konkrétní schránku
        """
        if delay > 0:
            time.sleep(delay)
            
        cmd = [
            "python", self.boxctl_path,
            "-d", self.device,
            "-a", "unlock",
            "-v", f"{box_id:02d}"
        ]
        
        start_time = time.time()
        print(f"[{time.strftime('%H:%M:%S')}] Posílám unlock příkaz na schránku {box_id}")
        
        try:
            # Spustíme příkaz s timeoutem
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=40,  # timeout o něco delší než v boxctl
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            command_info = {
                'box_id': box_id,
                'start_time': start_time,
                'end_time': end_time,
                'duration': duration,
                'return_code': result.returncode,
                'stdout': result.stdout.strip(),
                'stderr': result.stderr.strip(),
                'success': result.returncode == 0
            }
            
            if result.returncode == 0:
                print(f"[{time.strftime('%H:%M:%S')}] ✓ Schránka {box_id} - úspěch ({duration:.2f}s)")
            else:
                print(f"[{time.strftime('%H:%M:%S')}] ✗ Schránka {box_id} - chyba ({duration:.2f}s): {result.stderr}")
                
            return command_info
            
        except subprocess.TimeoutExpired:
            end_time = time.time()
            duration = end_time - start_time
            print(f"[{time.strftime('%H:%M:%S')}] ⏰ Schránka {box_id} - timeout ({duration:.2f}s)")
            
            return {
                'box_id': box_id,
                'start_time': start_time,
                'end_time': end_time,
                'duration': duration,
                'return_code': -1,
                'stdout': '',
                'stderr': 'TIMEOUT',
                'success': False
            }
        except Exception as e:
            print(f"[{time.strftime('%H:%M:%S')}] ❌ Schránka {box_id} - výjimka: {e}")
            return {
                'box_id': box_id,
                'start_time': start_time,
                'end_time': time.time(),
                'duration': time.time() - start_time,
                'return_code': -2,
                'stdout': '',
                'stderr': str(e),
                'success': False
            }

    def simulate_rapid_commands(self, box_ids: List[int], delay_between: float = 0.1):
        """
        Simuluje rychlé posílání příkazů za sebou (hlavní příčina problému)
        """
        print(f"\n=== SIMULACE 1: Rychlé příkazy za sebou (delay {delay_between}s) ===")
        print(f"Posílám unlock příkazy na schránky: {box_ids}")
        
        threads = []
        results = []
        
        for i, box_id in enumerate(box_ids):
            # Spustíme každý příkaz v samostatném vlákně s malým zpožděním
            thread = threading.Thread(
                target=lambda bid=box_id, d=i*delay_between: results.append(
                    self.send_unlock_command(bid, d)
                )
            )
            threads.append(thread)
            thread.start()
        
        # Počkáme na dokončení všech vláken
        for thread in threads:
            thread.join()
            
        return results

    def simulate_interrupted_commands(self, box_ids: List[int]):
        """
        Simuluje přerušené příkazy (Ctrl+C během běhu)
        """
        print(f"\n=== SIMULACE 2: Přerušené příkazy ===")
        print("Spustím příkaz a přeruším ho po 2 sekundách...")
        
        for box_id in box_ids:
            cmd = [
                "python", self.boxctl_path,
                "-d", self.device,
                "-a", "unlock", 
                "-v", f"{box_id:02d}"
            ]
            
            print(f"[{time.strftime('%H:%M:%S')}] Spouštím příkaz pro schránku {box_id}...")
            
            try:
                # Spustíme proces
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=os.path.dirname(os.path.abspath(__file__))
                )
                
                # Počkáme 2 sekundy a pak proces zabijeme
                time.sleep(2)
                process.terminate()
                
                print(f"[{time.strftime('%H:%M:%S')}] ⚡ Proces pro schránku {box_id} přerušen")
                
                # Krátká pauza mezi přerušenými příkazy
                time.sleep(0.5)
                
            except Exception as e:
                print(f"[{time.strftime('%H:%M:%S')}] ❌ Chyba při přerušování: {e}")

    def simulate_mixed_commands(self, box_ids: List[int]):
        """
        Simuluje smíšené příkazy (unlock + jiné akce)
        """
        print(f"\n=== SIMULACE 3: Smíšené příkazy ===")
        
        actions = [
            ("unlock", "01"),
            ("get_door_state", "01"), 
            ("unlock", "02"),
            ("heart_beat", None),
            ("unlock", "03")
        ]
        
        for action, value in actions:
            cmd = ["python", self.boxctl_path, "-d", self.device, "-a", action]
            if value:
                cmd.extend(["-v", value])
                
            print(f"[{time.strftime('%H:%M:%S')}] Posílám {action} {value or ''}")
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"[{time.strftime('%H:%M:%S')}] ✓ {action} - OK")
                else:
                    print(f"[{time.strftime('%H:%M:%S')}] ✗ {action} - chyba")
            except subprocess.TimeoutExpired:
                print(f"[{time.strftime('%H:%M:%S')}] ⏰ {action} - timeout")
            except Exception as e:
                print(f"[{time.strftime('%H:%M:%S')}] ❌ {action} - výjimka: {e}")
                
            # Velmi krátká pauza
            time.sleep(0.05)

    def print_summary(self, results: List[dict]):
        """
        Vypíše souhrn výsledků
        """
        print(f"\n=== SOUHRN VÝSLEDKŮ ===")
        print(f"Celkem příkazů: {len(results)}")
        
        successful = [r for r in results if r['success']]
        failed = [r for r in results if not r['success']]
        
        print(f"Úspěšné: {len(successful)}")
        print(f"Neúspěšné: {len(failed)}")
        
        if results:
            avg_duration = sum(r['duration'] for r in results) / len(results)
            max_duration = max(r['duration'] for r in results)
            min_duration = min(r['duration'] for r in results)
            
            print(f"Průměrná doba: {avg_duration:.2f}s")
            print(f"Nejdelší doba: {max_duration:.2f}s")
            print(f"Nejkratší doba: {min_duration:.2f}s")
            
        if failed:
            print(f"\nNeúspěšné příkazy:")
            for r in failed:
                print(f"  Schránka {r['box_id']}: {r['stderr']}")

def main():
    if len(sys.argv) < 2:
        print("Použití: python simulate_buffer_problem.py <COM_PORT>")
        print("Příklad: python simulate_buffer_problem.py COM5")
        sys.exit(1)
        
    device = sys.argv[1]
    simulator = SerialBufferSimulator(device)
    
    print("🔧 SIMULÁTOR PROBLÉMŮ SE SÉRIOVÝM PORTEM")
    print("=" * 50)
    print(f"Zařízení: {device}")
    print(f"Čas spuštění: {time.strftime('%H:%M:%S')}")
    
    # Test schránek
    test_boxes = [1, 2, 3, 4, 5]
    
    try:
        # Simulace 1: Rychlé příkazy (hlavní problém)
        results1 = simulator.simulate_rapid_commands(test_boxes, delay_between=0.1)
        
        # Krátká pauza
        time.sleep(2)
        
        # Simulace 2: Přerušené příkazy
        simulator.simulate_interrupted_commands([6, 7])
        
        # Krátká pauza  
        time.sleep(2)
        
        # Simulace 3: Smíšené příkazy
        simulator.simulate_mixed_commands(test_boxes)
        
        # Souhrn
        simulator.print_summary(results1)
        
        print(f"\n⚠️  OČEKÁVANÝ VÝSLEDEK:")
        print("- Některé příkazy mohou 'viset' nebo skončit timeoutem")
        print("- Schránky se mohou otevřít až s velkým zpožděním")
        print("- Příkazy se mohou zpracovávat v neočekávaném pořadí")
        print("- Sériový port může zůstat 'zanešený' starými příkazy")
        
    except KeyboardInterrupt:
        print(f"\n⚡ Simulace přerušena uživatelem")
    except Exception as e:
        print(f"\n❌ Chyba během simulace: {e}")

if __name__ == "__main__":
    main()
