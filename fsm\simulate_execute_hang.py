#!/usr/bin/env python3
"""
Skript pro simulaci zaseknutí execute_command() po vyprintování "ano 1"
Nastaví podmínky tak, aby se funkce zasekla na await loop.run_in_executor()
"""

import asyncio
import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor

# Přidáme cestu k managers
sys.path.append(os.path.join(os.path.dirname(__file__), 'managers'))

from electronics_api import execute_command

class ExecuteCommandHangSimulator:
    def __init__(self):
        self.original_executor = None
        self.blocked_executor = None
        
    def setup_hang_conditions(self):
        """
        Nastav<PERSON> podm<PERSON>ky, které způsobí zaseknutí execute_command()
        """
        print("🔧 Nastavuji podmínky pro zaseknutí execute_command()...")
        
        # Metoda 1: Vyčerpání ThreadPoolExecutor
        self._exhaust_thread_pool()
        
        # Metoda 2: Vyt<PERSON><PERSON><PERSON><PERSON> blokujícího executoru
        self._create_blocking_executor()
        
        print("✅ Podmínky nastaveny - execute_command() by se m<PERSON><PERSON> zasek<PERSON>ut po 'ano 1'")
    
    def _exhaust_thread_pool(self):
        """
        Vyčerpá default ThreadPoolExecutor spuštěním dlouho běžících tasků
        """
        print("   📦 Vyčerpávám ThreadPoolExecutor...")
        
        def long_running_task(task_id):
            print(f"      🔄 Blokující task {task_id} běží...")
            time.sleep(300)  # 5 minut blokování
            return f"task_{task_id}_done"
        
        # Spustíme více tasků než je default pool size (obvykle 5)
        loop = asyncio.get_event_loop()
        
        for i in range(8):  # Více než default pool size
            future = loop.run_in_executor(None, long_running_task, i)
            # Nebudeme čekat na dokončení - necháme je běžet
        
        print("   ✅ ThreadPoolExecutor vyčerpán")
    
    def _create_blocking_executor(self):
        """
        Vytvoří vlastní executor, který bude blokovaný
        """
        print("   🔒 Vytvářím blokující executor...")
        
        self.blocked_executor = ThreadPoolExecutor(max_workers=1)
        
        def blocking_task():
            print("      🚫 Blokující task v executoru...")
            time.sleep(300)  # 5 minut
            return "blocked"
        
        # Spustíme blokující task
        future = self.blocked_executor.submit(blocking_task)
        
        print("   ✅ Blokující executor vytvořen")
    
    async def test_execute_command_hang(self, device="COM5"):
        """
        Testuje, zda se execute_command() zasekne
        """
        print(f"\n🧪 TESTOVÁNÍ ZASEKNUTÍ execute_command()")
        print("=" * 50)
        
        # Příkaz pro testování
        cmd = ["python", "boxctl-carel.py", "-d", device, "-a", "heart_beat"]
        
        print("🚀 Spouštím execute_command()...")
        print("   Sledujte výstup:")
        print("   - Měli byste vidět 'ano 1'")
        print("   - 'ano 2' by se NEMĚLO objevit (funkce se zasekne)")
        print("   - Po timeoutu bude test ukončen")
        print()
        
        try:
            # Spustíme s timeoutem
            result = await asyncio.wait_for(
                execute_command(cmd, "hang_test"), 
                timeout=10
            )
            
            print("❌ NEOČEKÁVANÝ VÝSLEDEK: execute_command() se nezasekla!")
            print(f"   Výsledek: {result}")
            
        except asyncio.TimeoutError:
            print("✅ ÚSPĚCH: execute_command() se zasekla po 'ano 1'!")
            print("   Funkce čeká na volný thread v ThreadPoolExecutor")
            print("   Toto je přesně ten problém, který jste popisovali")
            
        except Exception as e:
            print(f"⚠️  Jiná chyba: {e}")
    
    async def test_multiple_hangs(self, device="COM5", count=3):
        """
        Testuje více současných volání - všechny by se měly zaseknout
        """
        print(f"\n🔥 TEST VÍCE SOUČASNÝCH VOLÁNÍ ({count}x)")
        print("=" * 50)
        
        cmd = ["python", "boxctl-carel.py", "-d", device, "-a", "heart_beat"]
        
        tasks = []
        for i in range(count):
            task = asyncio.create_task(execute_command(cmd, f"hang_test_{i}"))
            tasks.append(task)
            print(f"🚀 Spuštěn task {i}")
        
        print(f"\n⏰ Čekám {count} sekund na výsledky...")
        
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks), 
                timeout=count + 2
            )
            
            print("❌ NEOČEKÁVANÝ VÝSLEDEK: Některé tasky se nezasekly!")
            successful = sum(1 for r in results if r[2] is None)
            print(f"   Dokončené: {successful}/{len(results)}")
            
        except asyncio.TimeoutError:
            print("✅ ÚSPĚCH: Všechny tasky se zasekly!")
            
            # Zkontrolujeme stav tasků
            completed = sum(1 for task in tasks if task.done())
            hanging = len(tasks) - completed
            
            print(f"   Dokončené: {completed}")
            print(f"   Zaseknuté: {hanging}")
            
            if hanging > 0:
                print("   🎯 Toto reprodukuje váš původní problém!")
            
            # Zrušíme zaseknuté tasky
            for task in tasks:
                if not task.done():
                    task.cancel()
    
    def cleanup(self):
        """
        Uklidí zdroje
        """
        print("\n🧹 Uklízím...")
        
        if self.blocked_executor:
            self.blocked_executor.shutdown(wait=False)
        
        print("✅ Uklizeno")

async def main():
    if len(sys.argv) < 2:
        print("Použití:")
        print("  python simulate_execute_hang.py <COM_PORT> [test_type]")
        print("")
        print("Test types:")
        print("  single   - Test jednoho volání (default)")
        print("  multiple - Test více současných volání")
        print("")
        print("Příklady:")
        print("  python simulate_execute_hang.py COM5")
        print("  python simulate_execute_hang.py COM5 single")
        print("  python simulate_execute_hang.py COM5 multiple")
        sys.exit(1)
    
    device = sys.argv[1]
    test_type = sys.argv[2] if len(sys.argv) > 2 else "single"
    
    simulator = ExecuteCommandHangSimulator()
    
    try:
        print("🎯 SIMULÁTOR ZASEKNUTÍ execute_command()")
        print("=" * 60)
        print(f"Zařízení: {device}")
        print(f"Test: {test_type}")
        print()
        
        # Nastavíme podmínky pro zaseknutí
        simulator.setup_hang_conditions()
        
        # Počkáme chvilku, aby se podmínky ustálily
        await asyncio.sleep(2)
        
        if test_type == "single":
            await simulator.test_execute_command_hang(device)
        elif test_type == "multiple":
            await simulator.test_multiple_hangs(device, count=3)
        else:
            print(f"❌ Neznámý test type: {test_type}")
            return
        
        print(f"\n📋 SHRNUTÍ:")
        print("- Pokud jste viděli 'ano 1' ale ne 'ano 2', problém je reprodukován")
        print("- Příčina: ThreadPoolExecutor je vyčerpán/blokován")
        print("- Řešení: Použít vlastní executor nebo omezit současná volání")
        
    except KeyboardInterrupt:
        print("\n⚡ Test přerušen")
    except Exception as e:
        print(f"\n❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
    finally:
        simulator.cleanup()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"❌ Chyba v main: {e}")
        import traceback
        traceback.print_exc()
