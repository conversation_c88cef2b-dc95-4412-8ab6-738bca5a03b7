#!/usr/bin/env python3
"""
Test pro simulaci problému s execute_command() - funkce se "sekne" po "ano 1"
"""

import asyncio
import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor

# Přidáme cestu k managers
sys.path.append(os.path.join(os.path.dirname(__file__), 'managers'))

from electronics_api import execute_command

def log(msg):
    """Log s časem"""
    print(f"[{time.strftime('%H:%M:%S.%f')[:-3]}] {msg}")

async def test_normal_call():
    """Test 1: Normální volání - mělo by fungovat"""
    log("🟢 TEST 1: Normální async volání")
    try:
        result = await execute_command(["python", "--version"], "test")
        log(f"✅ Výsledek: {result[0][:50]}...")
    except Exception as e:
        log(f"❌ Chyba: {e}")

async def test_multiple_concurrent():
    """Test 2: <PERSON><PERSON><PERSON> so<PERSON>ch volání - může způsobit deadlock"""
    log("🟡 TEST 2: Více současných volání (může způsobit deadlock)")
    
    tasks = []
    for i in range(5):
        task = asyncio.create_task(
            execute_command(["python", "--version"], f"test_{i}")
        )
        tasks.append(task)
        log(f"   Spuštěn task {i}")
    
    try:
        results = await asyncio.gather(*tasks, timeout=10)
        log(f"✅ Všechny tasky dokončeny: {len(results)}")
    except asyncio.TimeoutError:
        log("⏰ TIMEOUT! Některé tasky se zasekly")
        for i, task in enumerate(tasks):
            if not task.done():
                log(f"   Task {i} stále běží - ZASEKNUTÝ!")
                task.cancel()
    except Exception as e:
        log(f"❌ Chyba: {e}")

def sync_wrapper_bad():
    """Test 3: Špatný způsob volání z sync kódu"""
    log("🔴 TEST 3: Špatné volání z sync kódu (způsobí deadlock)")
    
    try:
        # Toto je ŠPATNĚ - vytvoří nový event loop v threadu, který už má loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            execute_command(["python", "--version"], "sync_test")
        )
        log(f"✅ Výsledek: {result}")
        loop.close()
    except Exception as e:
        log(f"❌ Chyba: {e}")

async def test_executor_exhaustion():
    """Test 4: Vyčerpání executor pool"""
    log("🟠 TEST 4: Vyčerpání ThreadPoolExecutor")
    
    # Vytvoříme hodně dlouho běžících tasků
    long_tasks = []
    for i in range(10):  # Více než default pool size
        task = asyncio.create_task(
            execute_command(["python", "-c", "import time; time.sleep(5)"], f"long_{i}")
        )
        long_tasks.append(task)
        log(f"   Spuštěn dlouhý task {i}")
    
    # Počkáme chvilku
    await asyncio.sleep(1)
    
    # Teď zkusíme spustit další task - měl by se zaseknout
    log("   Spouštím další task - měl by se zaseknout...")
    try:
        result = await asyncio.wait_for(
            execute_command(["python", "--version"], "should_hang"),
            timeout=3
        )
        log(f"✅ Nečekaně dokončeno: {result}")
    except asyncio.TimeoutError:
        log("⏰ POTVRZENO! Task se zasekl - executor pool je vyčerpán")
    
    # Uklidíme
    for task in long_tasks:
        task.cancel()
    
    try:
        await asyncio.gather(*long_tasks, return_exceptions=True)
    except:
        pass

def test_nested_loop():
    """Test 5: Nested event loop problém"""
    log("🔵 TEST 5: Nested event loop problém")
    
    def sync_function_with_async():
        """Sync funkce, která se snaží volat async kód"""
        try:
            # Toto způsobí problém pokud už běží event loop
            result = asyncio.run(execute_command(["python", "--version"], "nested"))
            log(f"✅ Výsledek: {result}")
        except RuntimeError as e:
            if "cannot be called from a running event loop" in str(e):
                log("⚠️  POTVRZENO! RuntimeError - už běží event loop")
            else:
                log(f"❌ Jiná RuntimeError: {e}")
        except Exception as e:
            log(f"❌ Chyba: {e}")
    
    # Spustíme v threadu
    thread = threading.Thread(target=sync_function_with_async)
    thread.start()
    thread.join()

async def test_blocking_executor():
    """Test 6: Blokující executor"""
    log("🟣 TEST 6: Blokující executor")
    
    # Vytvoříme vlastní executor s 1 threadem
    executor = ThreadPoolExecutor(max_workers=1)
    
    def blocking_task():
        log("   Blokující task začal...")
        time.sleep(10)  # Dlouhé blokování
        log("   Blokující task skončil")
        return "blocked"
    
    # Spustíme blokující task
    loop = asyncio.get_running_loop()
    blocking_future = loop.run_in_executor(executor, blocking_task)
    
    # Počkáme chvilku
    await asyncio.sleep(1)
    
    # Teď zkusíme execute_command - měl by se zaseknout pokud používá stejný executor
    log("   Spouštím execute_command...")
    try:
        result = await asyncio.wait_for(
            execute_command(["python", "--version"], "blocked_test"),
            timeout=3
        )
        log(f"✅ Dokončeno: {result}")
    except asyncio.TimeoutError:
        log("⏰ ZASEKNUTÍ! execute_command čeká na executor")
    
    # Uklidíme
    blocking_future.cancel()
    executor.shutdown(wait=False)

async def main():
    """Hlavní test funkce"""
    log("🧪 TEST ZASEKÁVÁNÍ execute_command()")
    log("=" * 60)
    
    # Test 1: Normální volání
    await test_normal_call()
    await asyncio.sleep(1)
    
    # Test 2: Více současných volání
    await test_multiple_concurrent()
    await asyncio.sleep(1)
    
    # Test 3: Špatné volání z sync kódu
    test_nested_loop()
    await asyncio.sleep(1)
    
    # Test 4: Vyčerpání executor pool
    await test_executor_exhaustion()
    await asyncio.sleep(1)
    
    # Test 6: Blokující executor
    await test_blocking_executor()
    
    log("\n📊 SHRNUTÍ MOŽNÝCH PŘÍČIN ZASEKNUTÍ:")
    log("1. 🔄 Více současných volání vyčerpá ThreadPoolExecutor")
    log("2. 🔒 Nested event loop problém")
    log("3. 🧵 Blokující task v executor poolu")
    log("4. ⚡ Event loop deadlock")
    log("5. 🏃 Volání z nesprávného kontextu")

def run_sync_test():
    """Spustí sync test, který může způsobit problém"""
    log("🔴 SYNC TEST: Pokus o volání z sync kontextu")
    sync_wrapper_bad()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "sync":
        # Spustí problematický sync test
        run_sync_test()
    else:
        # Spustí async testy
        try:
            asyncio.run(main())
        except KeyboardInterrupt:
            log("⚡ Test přerušen")
        except Exception as e:
            log(f"❌ Chyba v main: {e}")
            import traceback
            traceback.print_exc()
